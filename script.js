// Global Variables
let currentActivity = "";
let scores = { red: 0, blue: 0, green: 0, yellow: 0 };
let currentPuzzle = 0;
let currentQuestion = 0;
let readingSpeed = 200;
let readingTimer = null;
let currentWordIndex = 0;
let highlightGranularity = 1; // Default to 1 word
let minefieldTeam = 0;
let minefieldLives = { red: 3, blue: 3, green: 3, yellow: 3 };
let tttTeam = 0;
let jeopardyTeam = 0;
let feudTeam = 0;
let timer = null;
let timerSeconds = 30;

// Team names
const teams = ["red", "blue", "green", "yellow"];

// Data Arrays
const puzzles = [
    {
        question: "What time does <PERSON><PERSON> wake up?",
        image: createClockSVG(6, 30),
        answer: "6:30 AM",
        type: "clock"
    },
    {
        question: "Put <PERSON><PERSON>'s morning routine in order:",
        options: ["Have breakfast", "Wake up", "Go jogging", "Get dressed"],
        answer: ["Wake up", "Get dressed", "Have breakfast", "Go jogging"],
        type: "sequence"
    },
    {
        question: "Which basketball equipment does <PERSON><PERSON> use?",
        options: ["Soccer ball", "Basketball", "Tennis racket", "Swimming goggles"],
        answer: "Basketball",
        type: "multiple-choice"
    },
    {
        question: "What does O<PERSON> <PERSON> do after school?",
        answer: "Basketball practice",
        type: "text"
    },
    {
        question: "Match the daily routine with the time:",
        pairs: [
            ["Wake up", "6:30 AM"],
            ["School starts", "8:00 AM"],
            ["Lunch", "12:00 PM"],
            ["Basketball practice", "4:00 PM"]
        ],
        type: "matching"
    }
];

const memoryWords = [
    { correct: "wake up", scrambled: "ekaw pu" },
    { correct: "breakfast", scrambled: "katsaerfb" },
    { correct: "homework", scrambled: "krowemoh" },
    { correct: "practice", scrambled: "ecitcarp" },
    { correct: "basketball", scrambled: "llabteksab" },
    { correct: "school", scrambled: "loohcs" },
    { correct: "jogging", scrambled: "gniggoj" },
    { correct: "lunch", scrambled: "hcnul" }
];

const vocabQuestions = [
    {
        question: "What do you do when you're hungry in the morning?",
        options: ["Take a shower", "Have breakfast", "Go to bed", "Play basketball"],
        correct: 1,
        image: "🍳"
    },
    {
        question: "What do you do to stay healthy and strong?",
        options: ["Sleep all day", "Exercise and practice", "Eat only candy", "Watch TV"],
        correct: 1,
        image: "🏃‍♂️"
    },
    {
        question: "Where do you learn new things every day?",
        options: ["At the park", "At school", "In bed", "At the store"],
        correct: 1,
        image: "🏫"
    },
    {
        question: "What do you do with your friends during break time?",
        options: ["Sleep", "Play games", "Do homework", "Clean the house"],
        correct: 1,
        image: "🎮"
    },
    {
        question: "What do you do when you get home from school?",
        options: ["Go to sleep immediately", "Do homework", "Run away", "Break things"],
        correct: 1,
        image: "📚"
    }
];

const ojMayoStory = `He's young, he gets good grades at school, and he has lots of friends and a good relationship with his parents. He also plays basketball for Huntington High School, Virginia, USA. Life's good for OJ Mayo. But it isn't always easy!\n\n"I really want to be a professional basketball player and I know I have to work hard," he says. He wakes up at 6:30 am every day. He starts the day with a big breakfast, and then he goes jogging in the park for an hour. After that, it's time for school. When he finishes, he usually goes to basketball practice. The first team practices four times a week. OJ is always sad when they lose. "Well, I know it's only a game," he says, "but losing always makes me feel bad."\n\nSo, with such a full schedule, how does he manage? "Well, I never miss practice. I sometimes worry about school because I want to get good grades, but I'm well-organized so I never fall behind with my homework. I never stay out late. I always do my best and work hard, both at school and at basketball."\n\nAny advice for ambitious teenagers like himself? "Always stay motivated and never give up!" OJ says.`;

const comprehensionQuestions = [
    { q: "What does OJ Mayo want to be?", options: ["Teacher", "Professional basketball player", "Doctor", "Chef"], correct: 1 },
    { q: "What time does he wake up?", options: ["5:30", "6:30", "7:30", "8:30"], correct: 1 },
    { q: "How does he feel when his team loses?", options: ["Happy", "Excited", "Sad", "Angry"], correct: 2 },
    { q: "How many times does his team practice per week?", options: ["2", "3", "4", "5"], correct: 2 },
    { q: "What is his advice for teenagers?", options: ["Give up easily", "Stay motivated", "Sleep more", "Play less"], correct: 1 }
];

const gapFillText = [
    { text: "OJ Mayo is a young basketball ", gap: "player" },
    { text: "He wakes up at ", gap: "6:30 AM" },
    { text: "He goes ", gap: "jogging" },
    { text: "in the park for an hour.", gap: null },
    { text: "After school, he goes to basketball ", gap: "practice" },
    { text: "He always tries his ", gap: "best" },
    { text: "at school and basketball.", gap: null }
];

const minefieldWords = [
    "wake", "breakfast", "school", "practice", "homework", "jogging",
    "basketball", "grades", "friends", "morning", "evening", "lunch",
    "dinner", "shower", "sleep", "study", "play", "exercise",
    "team", "game", "win", "lose", "motivated", "organized",
    "schedule", "routine", "healthy", "strong", "ambitious", "professional",
    "relationship", "parents", "teacher", "student", "player", "coach"
];

const tttWords = [
    "wake", "eat", "play", "study", "run", "sleep", "work",
    "practice", "exercise", "read", "write", "listen", "speak",
    "watch", "help", "learn", "teach", "cook", "clean", "wash",
    "brush", "dress", "walk", "jump", "swim", "dance", "sing",
    "draw", "paint", "build", "fix", "drive", "ride", "fly",
    "climb", "throw", "catch", "kick", "hit", "push", "pull",
    "open", "close", "start", "stop", "begin", "finish", "win"
];

const jeopardyCategories = [
    "Daily Routines",
    "Basketball Facts", 
    "Time Expressions",
    "Grammar Challenge"
];

const jeopardyQuestions = {
    "Daily Routines": [
        { question: "What do you do first thing in the morning?", options: ["Sleep", "Wake up", "Have dinner", "Go to school"], correct: 1, points: 100 },
        { question: "What meal do you eat in the middle of the day?", options: ["Breakfast", "Dinner", "Lunch", "Snack"], correct: 2, points: 200 },
        { question: "What do you do to clean your body?", options: ["Take a shower", "Eat food", "Play games", "Do homework"], correct: 0, points: 300 },
        { question: "What do you do before going to bed?", options: ["Wake up", "Have breakfast", "Brush teeth", "Go to school"], correct: 2, points: 400 },
        { question: "What do you do to learn new things?", options: ["Sleep", "Study", "Play only", "Watch TV only"], correct: 1, points: 500 }
    ],
    "Basketball Facts": [
        { question: "How many players are on a basketball team on the court?", options: ["3", "5", "7", "11"], correct: 1, points: 100 },
        { question: "What do you call it when you put the ball in the basket?", options: ["Goal", "Point", "Score", "Shot"], correct: 2, points: 200 },
        { question: "What does OJ Mayo want to become?", options: ["Teacher", "Doctor", "Professional player", "Chef"], correct: 2, points: 300 },
        { question: "How many times does OJ's team practice per week?", options: ["2 times", "4 times", "6 times", "Every day"], correct: 1, points: 400 },
        { question: "What time does OJ Mayo wake up?", options: ["5:30", "6:30", "7:30", "8:30"], correct: 1, points: 500 }
    ],
    "Time Expressions": [
        { question: "What time is 'half past six'?", options: ["6:15", "6:30", "6:45", "7:00"], correct: 1, points: 100 },
        { question: "What do you say for 3:15?", options: ["Quarter past three", "Half past three", "Quarter to three", "Three fifteen"], correct: 0, points: 200 },
        { question: "When do most people eat lunch?", options: ["Morning", "Afternoon", "Evening", "Night"], correct: 1, points: 300 },
        { question: "What comes after morning?", options: ["Night", "Evening", "Afternoon", "Dawn"], correct: 2, points: 400 },
        { question: "How do you ask about someone's daily schedule?", options: ["Where do you go?", "What time do you...?", "How are you?", "What's your name?"], correct: 1, points: 500 }
    ],
    "Grammar Challenge": [
        { question: "Complete: 'I _____ basketball every day.'", options: ["play", "plays", "playing", "played"], correct: 0, points: 100 },
        { question: "Complete: 'He _____ up at 6:30.'", options: ["wake", "wakes", "waking", "woke"], correct: 1, points: 200 },
        { question: "Make negative: 'I like homework.'", options: ["I no like homework", "I don't like homework", "I not like homework", "I doesn't like homework"], correct: 1, points: 300 },
        { question: "Make question: 'You play basketball.'", options: ["You play basketball?", "Do you play basketball?", "Are you play basketball?", "Playing you basketball?"], correct: 1, points: 400 },
        { question: "Complete: 'What time _____ you wake up?'", options: ["do", "does", "are", "is"], correct: 0, points: 500 }
    ]
};

const feudCategories = [
    {
        name: "Things you do in the morning",
        answers: [
            { text: "Wake up", points: 35 },
            { text: "Brush teeth", points: 25 },
            { text: "Have breakfast", points: 20 },
            { text: "Take a shower", points: 15 },
            { text: "Get dressed", points: 5 }
        ]
    },
    {
        name: "Basketball equipment",
        answers: [
            { text: "Basketball", points: 35 },
            { text: "Basketball shoes", points: 25 },
            { text: "Jersey/uniform", points: 20 },
            { text: "Basketball hoop", points: 15 },
            { text: "Water bottle", points: 5 }
        ]
    },
    {
        name: "School activities",
        answers: [
            { text: "Study/Learn", points: 35 },
            { text: "Do homework", points: 25 },
            { text: "Play with friends", points: 20 },
            { text: "Eat lunch", points: 15 },
            { text: "Take tests", points: 5 }
        ]
    }
];

// Utility Functions
function createClockSVG(hours, minutes) {
    const hourAngle = (hours % 12) * 30 + (minutes / 60) * 30;
    const minuteAngle = minutes * 6;
    
    return `
        <svg width="150" height="150" viewBox="0 0 150 150">
            <circle cx="75" cy="75" r="70" fill="white" stroke="#333" stroke-width="3"/>
            <g stroke="#333" stroke-width="2">
                ${Array.from({length: 12}, (_, i) => {
                    const angle = i * 30;
                    const x1 = 75 + 60 * Math.cos((angle - 90) * Math.PI / 180);
                    const y1 = 75 + 60 * Math.sin((angle - 90) * Math.PI / 180);
                    const x2 = 75 + 50 * Math.cos((angle - 90) * Math.PI / 180);
                    const y2 = 75 + 50 * Math.sin((angle - 90) * Math.PI / 180);
                    return `<line x1="${x1}" y1="${y1}" x2="${x2}" y2="${y2}"/>`;
                }).join('')}
            </g>
            <line x1="75" y1="75" 
                  x2="${75 + 35 * Math.cos((hourAngle - 90) * Math.PI / 180)}" 
                  y2="${75 + 35 * Math.sin((hourAngle - 90) * Math.PI / 180)}" 
                  stroke="#333" stroke-width="4" stroke-linecap="round"/>
            <line x1="75" y1="75" 
                  x2="${75 + 45 * Math.cos((minuteAngle - 90) * Math.PI / 180)}" 
                  y2="${75 + 45 * Math.sin((minuteAngle - 90) * Math.PI / 180)}" 
                  stroke="#333" stroke-width="2" stroke-linecap="round"/>
            <circle cx="75" cy="75" r="5" fill="#333"/>
        </svg>
    `;
}

function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

function showScreen(screenId) {
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });
    document.getElementById(screenId).classList.add('active');
}

// Scoreboard Functions
function adjustScore(team, points) {
    scores[team] += points;
    if (scores[team] < 0) scores[team] = 0;
    updateScoreboard();
}

function resetScores() {
    Object.keys(scores).forEach(team => scores[team] = 0);
    updateScoreboard();
}

function updateScoreboard() {
    Object.keys(scores).forEach(team => {
        const scoreElement = document.querySelector(`.team.${team} .score`);
        if (scoreElement) {
            scoreElement.textContent = scores[team];
        }
    });
}

function toggleScoreboard() {
    const scoreboard = document.getElementById('scoreboard');
    scoreboard.style.display = scoreboard.style.display === 'none' ? 'flex' : 'none';
}

// Main Navigation
function startLesson() {
    showScreen('activity-menu');
}

function showActivity(activityId) {
    currentActivity = activityId;
    showScreen(activityId);
    
    switch(activityId) {
        case 'puzzles':
            initializePuzzles();
            break;
        case 'memory':
            initializeMemoryGame();
            break;
        case 'vocab-quiz':
            initializeVocabQuiz();
            break;
        case 'speed-reading':
            initializeSpeedReading();
            break;
        case 'minefield':
            initializeMinefield();
            break;
        case 'tic-tac-toe':
            initializeTicTacToe();
            break;
        case 'jeopardy':
            initializeJeopardy();
            break;
        case 'family-feud':
            initializeFamilyFeud();
            break;
    }
}

function backToMenu() {
    showScreen('activity-menu');
    // Reset any running timers
    if (readingTimer) {
        clearInterval(readingTimer);
        readingTimer = null;
    }
    if (timer) {
        clearInterval(timer);
        timer = null;
    }
}

// Logic Puzzles
function initializePuzzles() {
    currentPuzzle = 0;
    displayPuzzle();
}

function displayPuzzle() {
    const puzzle = puzzles[currentPuzzle];
    const container = document.getElementById('current-puzzle');

    // Reset game states
    selectedSequence = [];
    selectedMatchItems = { left: null, right: null };
    matchedPairs = [];

    let html = `<h3>${puzzle.question}</h3>`;
    
    switch(puzzle.type) {
        case 'clock':
            html += `<div class="puzzle-image">${puzzle.image}</div>`;
            break;
        case 'sequence':
            html += '<div class="sequence-options">';
            puzzle.options.forEach((option, index) => {
                html += `<div class="sequence-item" onclick="selectSequenceItem(this)" data-index="${index}" data-original="${option}">${option}</div>`;
            });
            html += '</div>';
            html += '<div class="sequence-answer" style="margin-top: 20px; min-height: 60px; border: 2px dashed #ccc; padding: 10px; border-radius: 5px;"><p>Click items in the correct order:</p><div id="sequence-selected"></div></div>';
            html += '<button onclick="checkSequenceAnswer()" style="margin-top: 10px; padding: 8px 15px; background-color: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">Check Order</button>';
            html += '<button onclick="resetSequence()" style="margin-left: 10px; padding: 8px 15px; background-color: #f39c12; color: white; border: none; border-radius: 5px; cursor: pointer;">Reset</button>';
            break;
        case 'multiple-choice':
            html += '<div class="mc-options">';
            puzzle.options.forEach((option, index) => {
                html += `<button class="mc-option" onclick="selectOption(${index})">${option}</button>`;
            });
            html += '</div>';
            break;
        case 'text':
            html += '<input type="text" id="puzzle-input" placeholder="Type your answer here...">';
            html += '<button onclick="checkTextAnswer()" style="margin-left: 10px; padding: 8px 15px; background-color: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">Check Answer</button>';
            break;
        case 'matching':
            html += '<div class="matching-container">';
            html += '<div class="matching-instructions">Click to match the pairs!</div>';

            // Create left column (shuffled)
            const leftItems = shuffleArray(puzzle.pairs.map(pair => pair[0]));
            const rightItems = shuffleArray(puzzle.pairs.map(pair => pair[1]));

            html += '<div class="matching-columns">';
            html += '<div class="matching-left">';
            leftItems.forEach((item, index) => {
                html += `<div class="match-item left" onclick="selectMatchItem(this, 'left')" data-value="${item}">${item}</div>`;
            });
            html += '</div>';

            html += '<div class="matching-right">';
            rightItems.forEach((item, index) => {
                html += `<div class="match-item right" onclick="selectMatchItem(this, 'right')" data-value="${item}">${item}</div>`;
            });
            html += '</div>';
            html += '</div>';

            html += '<button onclick="checkAllMatches()" style="margin-top: 15px; padding: 8px 15px; background-color: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">Check All Matches</button>';
            html += '<button onclick="resetMatching()" style="margin-left: 10px; padding: 8px 15px; background-color: #f39c12; color: white; border: none; border-radius: 5px; cursor: pointer;">Reset</button>';
            html += '</div>';
            break;
    }
    
    container.innerHTML = html;
}

function selectOption(index) {
    const puzzle = puzzles[currentPuzzle];
    const buttons = document.querySelectorAll('.mc-option');

    // Disable all buttons
    buttons.forEach(btn => btn.disabled = true);

    // Check if correct
    if (puzzle.options[index] === puzzle.answer) {
        buttons[index].style.backgroundColor = '#2ecc71';
        adjustScore(teams[Math.floor(Math.random() * teams.length)], 1);
        alert('Correct!');
    } else {
        buttons[index].style.backgroundColor = '#e74c3c';
        // Show correct answer
        const correctIndex = puzzle.options.indexOf(puzzle.answer);
        if (correctIndex !== -1) {
            buttons[correctIndex].style.backgroundColor = '#2ecc71';
        }
        alert(`Incorrect! The correct answer is: ${puzzle.answer}`);
    }
}

function checkTextAnswer() {
    const puzzle = puzzles[currentPuzzle];
    const input = document.getElementById('puzzle-input');
    const userAnswer = input.value.trim().toLowerCase();
    const correctAnswer = puzzle.answer.toLowerCase();

    if (userAnswer === correctAnswer) {
        input.style.backgroundColor = '#d4edda';
        input.style.borderColor = '#2ecc71';
        adjustScore(teams[Math.floor(Math.random() * teams.length)], 1);
        alert('Correct!');
    } else {
        input.style.backgroundColor = '#f8d7da';
        input.style.borderColor = '#e74c3c';
        alert(`Incorrect! The correct answer is: ${puzzle.answer}`);
    }
    input.disabled = true;
}

let selectedSequence = [];

function selectSequenceItem(element) {
    if (element.classList.contains('selected')) return;

    element.classList.add('selected');
    element.style.backgroundColor = '#3498db';
    element.style.color = 'white';

    selectedSequence.push(element.dataset.original);

    const selectedDiv = document.getElementById('sequence-selected');
    selectedDiv.innerHTML = selectedSequence.map((item, index) => `${index + 1}. ${item}`).join('<br>');
}

function resetSequence() {
    selectedSequence = [];
    document.querySelectorAll('.sequence-item').forEach(item => {
        item.classList.remove('selected');
        item.style.backgroundColor = '';
        item.style.color = '';
    });
    document.getElementById('sequence-selected').innerHTML = '';
}

function checkSequenceAnswer() {
    const puzzle = puzzles[currentPuzzle];
    const correct = JSON.stringify(selectedSequence) === JSON.stringify(puzzle.answer);

    if (correct) {
        adjustScore(teams[Math.floor(Math.random() * teams.length)], 2);
        alert('Correct sequence!');
        document.querySelector('.sequence-answer').style.backgroundColor = '#d4edda';
    } else {
        alert(`Incorrect! The correct order is: ${puzzle.answer.join(' → ')}`);
        document.querySelector('.sequence-answer').style.backgroundColor = '#f8d7da';
    }
}

let selectedMatchItems = { left: null, right: null };
let matchedPairs = [];

function selectMatchItem(element, side) {
    // Clear previous selection for this side
    document.querySelectorAll(`.match-item.${side}.selected`).forEach(item => {
        item.classList.remove('selected');
    });

    // Select current item
    element.classList.add('selected');
    selectedMatchItems[side] = element;

    // If both sides selected, try to match
    if (selectedMatchItems.left && selectedMatchItems.right) {
        checkMatch();
    }
}

function checkMatch() {
    const puzzle = puzzles[currentPuzzle];
    const leftValue = selectedMatchItems.left.dataset.value;
    const rightValue = selectedMatchItems.right.dataset.value;

    // Check if this is a correct pair
    const isCorrectPair = puzzle.pairs.some(pair =>
        (pair[0] === leftValue && pair[1] === rightValue)
    );

    if (isCorrectPair) {
        // Mark as matched
        selectedMatchItems.left.classList.add('matched');
        selectedMatchItems.right.classList.add('matched');
        selectedMatchItems.left.classList.remove('selected');
        selectedMatchItems.right.classList.remove('selected');

        matchedPairs.push([leftValue, rightValue]);

        // Disable matched items
        selectedMatchItems.left.onclick = null;
        selectedMatchItems.right.onclick = null;

        adjustScore(teams[Math.floor(Math.random() * teams.length)], 1);

        // Check if all pairs matched
        if (matchedPairs.length === puzzle.pairs.length) {
            setTimeout(() => alert('All pairs matched correctly!'), 100);
        }
    } else {
        // Wrong match - flash red
        selectedMatchItems.left.classList.add('wrong');
        selectedMatchItems.right.classList.add('wrong');

        setTimeout(() => {
            selectedMatchItems.left.classList.remove('wrong', 'selected');
            selectedMatchItems.right.classList.remove('wrong', 'selected');
        }, 1000);
    }

    // Reset selection
    selectedMatchItems = { left: null, right: null };
}

function resetMatching() {
    matchedPairs = [];
    selectedMatchItems = { left: null, right: null };

    document.querySelectorAll('.match-item').forEach(item => {
        item.classList.remove('selected', 'matched', 'wrong');
        item.onclick = function() { selectMatchItem(this, this.classList.contains('left') ? 'left' : 'right'); };
    });
}

function checkAllMatches() {
    const puzzle = puzzles[currentPuzzle];
    if (matchedPairs.length === puzzle.pairs.length) {
        alert('Perfect! All pairs are correctly matched!');
        adjustScore(teams[Math.floor(Math.random() * teams.length)], 2);
    } else {
        alert(`You have ${matchedPairs.length} out of ${puzzle.pairs.length} pairs matched correctly.`);
    }
}

function showAnswer() {
    const puzzle = puzzles[currentPuzzle];
    if (puzzle.type === 'matching') {
        const pairStrings = puzzle.pairs.map(pair => `${pair[0]} → ${pair[1]}`);
        alert(`Correct pairs:\n${pairStrings.join('\n')}`);
    } else {
        alert(`Answer: ${Array.isArray(puzzle.answer) ? puzzle.answer.join(', ') : puzzle.answer}`);
    }
}

function nextPuzzle() {
    currentPuzzle = (currentPuzzle + 1) % puzzles.length;
    displayPuzzle();
}

function shufflePuzzles() {
    currentPuzzle = Math.floor(Math.random() * puzzles.length);
    displayPuzzle();
}

// Memory Game
let flippedCards = [];
let matchedCards = [];

function initializeMemoryGame() {
    matchedCards = [];
    shuffleMemoryCards();
}

function shuffleMemoryCards() {
    const gameGrid = document.getElementById('memory-grid');
    gameGrid.innerHTML = '';
    flippedCards = [];

    const allCards = [...memoryWords, ...memoryWords]; // Duplicate for pairs
    const shuffledCards = shuffleArray(allCards);

    shuffledCards.forEach((card, index) => {
        const cardElement = document.createElement('button');
        cardElement.classList.add('memory-card');
        cardElement.dataset.index = index;
        cardElement.dataset.correct = card.correct;
        cardElement.textContent = '?'; // Hidden initially
        cardElement.onclick = () => flipCard(cardElement, card.scrambled);
        gameGrid.appendChild(cardElement);
    });
}

function flipCard(cardElement, scrambledText) {
    if (flippedCards.length < 2 && !cardElement.classList.contains('flipped') && !cardElement.classList.contains('matched')) {
        cardElement.classList.add('flipped');
        cardElement.textContent = scrambledText;
        flippedCards.push(cardElement);

        if (flippedCards.length === 2) {
            setTimeout(checkForMatch, 1000);
        }
    }
}

function checkForMatch() {
    const [card1, card2] = flippedCards;
    if (card1.dataset.correct === card2.dataset.correct) {
        card1.classList.add('matched');
        card2.classList.add('matched');
        matchedCards.push(card1, card2);
        adjustScore(teams[Math.floor(Math.random() * teams.length)], 1); // Award a random team
    } else {
        card1.classList.remove('flipped');
        card2.classList.remove('flipped');
        card1.textContent = '?';
        card2.textContent = '?';
    }
    flippedCards = [];
}

function resetMemoryGame() {
    shuffleMemoryCards();
}

// Vocabulary Quiz
function initializeVocabQuiz() {
    currentQuestion = 0;
    displayVocabQuestion();
}

function displayVocabQuestion() {
    const quiz = vocabQuestions[currentQuestion];
    const questionDisplay = document.getElementById('question-display');
    const optionsDisplay = document.getElementById('quiz-options');

    questionDisplay.innerHTML = `
        <div class="question-icon">${quiz.image}</div>
        <h3>${quiz.question}</h3>
    `;
    optionsDisplay.innerHTML = '';

    quiz.options.forEach((option, index) => {
        const button = document.createElement('button');
        button.classList.add('quiz-option');
        button.textContent = option;
        button.onclick = () => checkVocabAnswer(index, quiz.correct, button);
        optionsDisplay.appendChild(button);
    });
}

function checkVocabAnswer(selectedIndex, correctAnswer, button) {
    document.querySelectorAll('.quiz-option').forEach(btn => btn.disabled = true);
    if (selectedIndex === correctAnswer) {
        button.classList.add('correct');
        adjustScore(teams[Math.floor(Math.random() * teams.length)], 1); // Award a random team
    } else {
        button.classList.add('incorrect');
        document.querySelectorAll('.quiz-option')[correctAnswer].classList.add('correct');
    }
}

function nextQuestion() {
    currentQuestion = (currentQuestion + 1) % vocabQuestions.length;
    displayVocabQuestion();
    document.querySelectorAll('.quiz-option').forEach(btn => btn.disabled = false);
}

function shuffleQuestions() {
    currentQuestion = Math.floor(Math.random() * vocabQuestions.length);
    displayVocabQuestion();
    document.querySelectorAll('.quiz-option').forEach(btn => btn.disabled = false);
}

// Speed Reading
let words = [];
let currentSentenceIndex = 0;

function initializeSpeedReading() {
    const readingTextDiv = document.getElementById('reading-text');
    readingTextDiv.innerHTML = '';
    currentWordIndex = 0;
    currentSentenceIndex = 0;

    // Split story into sentences and then words
    const sentences = ojMayoStory.split(/\n|(?<=[.?!])\s+/).filter(s => s.trim() !== '');
    words = [];
    sentences.forEach(sentence => {
        const sentenceWords = sentence.split(/\s+/).filter(w => w.trim() !== '');
        sentenceWords.forEach(word => {
            words.push({ word: word, sentenceIndex: sentences.indexOf(sentence) });
        });
    });

    // Render the full text with each word wrapped in a span
    words.forEach((wordObj, index) => {
        const span = document.createElement('span');
        span.classList.add('word');
        span.textContent = wordObj.word + ' ';
        span.dataset.index = index;
        readingTextDiv.appendChild(span);
    });

    // Initialize comprehension questions and gap fills
    displayComprehensionQuestions();
    displayGapFill();
}

function updateSpeed() {
    readingSpeed = document.getElementById('speed-slider').value;
    document.getElementById('speed-display').textContent = readingSpeed;
    if (readingTimer) {
        pauseReading();
        startReading();
    }
}

function updateHighlightGranularity() {
    highlightGranularity = document.getElementById('highlight-granularity').value;
    resetReading(); // Reset to apply new highlighting
}

function startReading() {
    if (readingTimer) return; // Already running

    const delay = 60000 / readingSpeed; // Milliseconds per word

    readingTimer = setInterval(() => {
        if (currentWordIndex >= words.length) {
            pauseReading();
            return;
        }

        // Remove previous highlights
        document.querySelectorAll('.word.highlight').forEach(span => {
            span.classList.remove('highlight');
        });

        let wordsToHighlight = [];
        if (highlightGranularity === 'sentence') {
            const currentSentence = words[currentWordIndex].sentenceIndex;
            wordsToHighlight = words.filter(wordObj => wordObj.sentenceIndex === currentSentence);
        } else {
            const numWords = parseInt(highlightGranularity);
            for (let i = 0; i < numWords && (currentWordIndex + i) < words.length; i++) {
                wordsToHighlight.push(words[currentWordIndex + i]);
            }
        }

        wordsToHighlight.forEach(wordObj => {
            const span = document.querySelector(`.word[data-index="${words.indexOf(wordObj)}"]`);
            if (span) {
                span.classList.add('highlight');
                // Scroll into view if needed
                span.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });

        if (highlightGranularity === 'sentence') {
            currentWordIndex = words.indexOf(wordsToHighlight[wordsToHighlight.length - 1]) + 1;
        } else {
            currentWordIndex += parseInt(highlightGranularity);
        }

    }, delay * (highlightGranularity === 'sentence' ? 1 : parseInt(highlightGranularity))); // Adjust interval based on granularity
}

function pauseReading() {
    if (readingTimer) {
        clearInterval(readingTimer);
        readingTimer = null;
    }
}

function resetReading() {
    pauseReading();
    currentWordIndex = 0;
    document.querySelectorAll('.word.highlight').forEach(span => {
        span.classList.remove('highlight');
    });
    document.getElementById('reading-text').scrollTop = 0; // Scroll to top
}

function displayComprehensionQuestions() {
    const container = document.getElementById('comprehension-questions');
    container.innerHTML = '';
    comprehensionQuestions.forEach((q, qIndex) => {
        const qDiv = document.createElement('div');
        qDiv.classList.add('comprehension-question');
        qDiv.innerHTML = `<h4>${qIndex + 1}. ${q.q}</h4>`;
        const optionsDiv = document.createElement('div');
        optionsDiv.classList.add('comp-options');
        q.options.forEach((option, oIndex) => {
            const btn = document.createElement('button');
            btn.textContent = option;
            btn.onclick = () => checkComprehensionAnswer(btn, oIndex, q.correct);
            optionsDiv.appendChild(btn);
        });
        qDiv.appendChild(optionsDiv);
        container.appendChild(qDiv);
    });
}

function checkComprehensionAnswer(button, selectedIndex, correctAnswer) {
    const optionsDiv = button.closest('.comp-options');
    Array.from(optionsDiv.children).forEach(btn => btn.disabled = true);
    if (selectedIndex === correctAnswer) {
        button.classList.add('correct');
        adjustScore(teams[Math.floor(Math.random() * teams.length)], 1); // Award a random team
    } else {
        button.classList.add('incorrect');
        optionsDiv.children[correctAnswer].classList.add('correct');
    }
}

function showAllComprehensionAnswers() {
    comprehensionQuestions.forEach((q, qIndex) => {
        const optionsDiv = document.querySelectorAll('.comprehension-question')[qIndex].querySelector('.comp-options');
        Array.from(optionsDiv.children).forEach((btn, oIndex) => {
            btn.disabled = true;
            if (oIndex === q.correct) {
                btn.classList.add('correct');
            } else {
                btn.classList.remove('correct');
            }
        });
    });
}

function displayGapFill() {
    const container = document.getElementById('gap-fill-text');
    container.innerHTML = '';
    gapFillText.forEach((item, index) => {
        if (item.text) {
            container.appendChild(document.createTextNode(item.text));
        }
        if (item.gap) {
            const span = document.createElement('span');
            span.classList.add('gap-word');
            span.textContent = '_______'; // Placeholder
            span.dataset.answer = item.gap;
            span.onclick = () => revealGap(span);
            container.appendChild(span);
        }
    });
}

function revealGap(spanElement) {
    spanElement.textContent = spanElement.dataset.answer;
    spanElement.classList.add('revealed-gap');
    spanElement.onclick = null; // Make it not clickable after revealing
}

function showAllGapFillAnswers() {
    document.querySelectorAll('.gap-word').forEach(span => {
        revealGap(span);
    });
}

// Minefield
let minefieldGrid = [];

function initializeMinefield() {
    minefieldGrid = [];
    minefieldLives = { red: 3, blue: 3, green: 3, yellow: 3 };
    minefieldTeam = 0;
    updateMinefieldDisplay();
    generateMinefieldGrid();
}

function updateMinefieldDisplay() {
    document.getElementById('current-minefield-team').textContent = teams[minefieldTeam].charAt(0).toUpperCase() + teams[minefieldTeam].slice(1);
    document.getElementById('red-lives').textContent = minefieldLives.red;
    document.getElementById('blue-lives').textContent = minefieldLives.blue;
    document.getElementById('green-lives').textContent = minefieldLives.green;
    document.getElementById('yellow-lives').textContent = minefieldLives.yellow;
}

function generateMinefieldGrid() {
    const gridContainer = document.getElementById('minefield-grid');
    gridContainer.innerHTML = '';
    minefieldGrid = [];

    const totalCells = 36; // 6x6 grid
    const numBombs = 6; // Number of bombs
    const shuffledWords = shuffleArray(minefieldWords);

    let cells = [];
    for (let i = 0; i < totalCells; i++) {
        cells.push({ word: shuffledWords[i % shuffledWords.length], isBomb: false, revealed: false });
    }

    // Place bombs randomly
    let bombsPlaced = 0;
    while (bombsPlaced < numBombs) {
        const randomIndex = Math.floor(Math.random() * totalCells);
        if (!cells[randomIndex].isBomb) {
            cells[randomIndex].isBomb = true;
            bombsPlaced++;
        }
    }

    minefieldGrid = cells;

    minefieldGrid.forEach((cell, index) => {
        const cellElement = document.createElement('button');
        cellElement.classList.add('minefield-cell');
        cellElement.textContent = '?';
        cellElement.onclick = () => revealMinefieldCell(index);
        gridContainer.appendChild(cellElement);
    });
}

function revealMinefieldCell(index) {
    const cell = minefieldGrid[index];
    const cellElement = document.querySelectorAll('.minefield-cell')[index];

    if (cell.revealed) return;

    cell.revealed = true;
    cellElement.classList.add('revealed');
    cellElement.textContent = cell.word;
    cellElement.onclick = null; // Disable further clicks

    if (cell.isBomb) {
        cellElement.classList.add('bomb');
        minefieldLives[teams[minefieldTeam]]--;
        alert(`${teams[minefieldTeam].charAt(0).toUpperCase() + teams[minefieldTeam].slice(1)} Team hit a bomb! Lives remaining: ${minefieldLives[teams[minefieldTeam]]}`);
        if (minefieldLives[teams[minefieldTeam]] <= 0) {
            alert(`${teams[minefieldTeam].charAt(0).toUpperCase() + teams[minefieldTeam].slice(1)} Team is out of lives!`);
            // Optionally end game or remove team
        }
    } else {
        adjustScore(teams[minefieldTeam], 1); // Award point for safe word
    }
    updateMinefieldDisplay();
    minefieldTeam = (minefieldTeam + 1) % teams.length; // Next team's turn
    updateMinefieldDisplay();
}

function resetMinefield() {
    initializeMinefield();
}

// Tic-Tac-Toe
let tttGrid = [];

function initializeTicTacToe() {
    tttGrid = Array(49).fill(null); // 7x7 grid
    tttTeam = 0;
    generateTicTacToeGrid();
    updateTicTacToeDisplay();
}

function updateTicTacToeDisplay() {
    document.getElementById('current-ttt-team').textContent = teams[tttTeam].charAt(0).toUpperCase() + teams[tttTeam].slice(1);
}

function generateTicTacToeGrid() {
    const gridContainer = document.getElementById('ttt-grid');
    gridContainer.innerHTML = '';

    const shuffledWords = shuffleArray(tttWords);
    const symbols = ['+', '-', 'x', '/', '=', '?', '!'];

    for (let i = 0; i < 49; i++) {
        const cellElement = document.createElement('button');
        cellElement.classList.add('ttt-cell');
        const word = shuffledWords[i % shuffledWords.length];
        const symbol = symbols[Math.floor(Math.random() * symbols.length)];
        cellElement.innerHTML = `<span>${word}</span><span>${symbol}</span>`;
        cellElement.onclick = () => selectTicTacToeCell(i, cellElement);
        gridContainer.appendChild(cellElement);
    }
}

function selectTicTacToeCell(index, cellElement) {
    if (tttGrid[index] === null) {
        tttGrid[index] = teams[tttTeam];
        cellElement.classList.add(teams[tttTeam]);
        cellElement.onclick = null; // Disable further clicks
        adjustScore(teams[tttTeam], 2); // Award points for taking a cell
        tttTeam = (tttTeam + 1) % teams.length; // Next team's turn
        updateTicTacToeDisplay();
    } else {
        alert('This cell is already taken!');
    }
}

function resetTicTacToe() {
    initializeTicTacToe();
}

// Jeopardy
let jeopardyBoardState = {};
let currentJeopardyQuestion = null;

function initializeJeopardy() {
    jeopardyBoardState = {};
    jeopardyTeam = 0;
    updateJeopardyDisplay();
    generateJeopardyBoard();
}

function updateJeopardyDisplay() {
    document.getElementById('current-jeopardy-team').textContent = teams[jeopardyTeam].charAt(0).toUpperCase() + teams[jeopardyTeam].slice(1);
}

function generateJeopardyBoard() {
    const boardContainer = document.getElementById('jeopardy-board');
    boardContainer.innerHTML = '';

    jeopardyCategories.forEach(category => {
        const categoryDiv = document.createElement('div');
        categoryDiv.classList.add('jeopardy-category');
        categoryDiv.textContent = category;
        boardContainer.appendChild(categoryDiv);
    });

    [100, 200, 300, 400, 500].forEach(points => {
        jeopardyCategories.forEach(category => {
            const cellElement = document.createElement('button');
            cellElement.classList.add('jeopardy-cell');
            cellElement.textContent = points;
            cellElement.dataset.category = category;
            cellElement.dataset.points = points;
            cellElement.onclick = () => openJeopardyQuestion(category, points, cellElement);
            boardContainer.appendChild(cellElement);
            jeopardyBoardState[`${category}-${points}`] = false; // Not used yet
        });
    });
}

function openJeopardyQuestion(category, points, cellElement) {
    if (jeopardyBoardState[`${category}-${points}`]) return; // Already used

    const questions = jeopardyQuestions[category];
    const question = questions.find(q => q.points === points);
    currentJeopardyQuestion = question;

    const modal = document.getElementById('question-modal');
    const modalQuestion = document.getElementById('modal-question');
    const modalOptions = document.getElementById('modal-options');

    modalQuestion.innerHTML = `<h3>${category} - ${points} Points</h3><p>${question.question}</p>`;
    modalOptions.innerHTML = '';

    question.options.forEach((option, index) => {
        const button = document.createElement('button');
        button.textContent = option;
        button.onclick = () => checkJeopardyAnswer(button, index, question.correct, points, cellElement);
        modalOptions.appendChild(button);
    });

    modal.classList.add('active');
    jeopardyBoardState[`${category}-${points}`] = true; // Mark as used
    cellElement.classList.add('used');
    cellElement.onclick = null; // Disable further clicks

    // Start timer
    timerSeconds = 30;
    document.getElementById('timer-seconds').textContent = timerSeconds;
    document.getElementById('timer-display').classList.remove('hidden');
    timer = setInterval(() => {
        timerSeconds--;
        document.getElementById('timer-seconds').textContent = timerSeconds;
        if (timerSeconds <= 0) {
            clearInterval(timer);
            timer = null;
            alert('Time is up! No points for this question.');
            closeModal();
            nextJeopardyTeam();
        }
    }, 1000);
}

function checkJeopardyAnswer(button, selectedIndex, correctAnswer, points, cellElement) {
    clearInterval(timer);
    timer = null;
    document.getElementById('timer-display').classList.add('hidden');

    document.querySelectorAll('#modal-options button').forEach(btn => btn.disabled = true);

    if (selectedIndex === correctAnswer) {
        button.classList.add('correct');
        adjustScore(teams[jeopardyTeam], points);
        alert('Correct! You get ' + points + ' points!');
    } else {
        button.classList.add('incorrect');
        document.querySelectorAll('#modal-options button')[correctAnswer].classList.add('correct');
        adjustScore(teams[jeopardyTeam], -points); // Deduct points for incorrect answer
        alert('Incorrect! You lose ' + points + ' points.');
    }
    // Allow user to see correct answer for a moment, then close
    setTimeout(() => {
        closeModal();
        nextJeopardyTeam();
    }, 2000);
}

function closeModal() {
    document.getElementById('question-modal').classList.remove('active');
    clearInterval(timer);
    timer = null;
    document.getElementById('timer-display').classList.add('hidden');
}

function nextJeopardyTeam() {
    jeopardyTeam = (jeopardyTeam + 1) % teams.length;
    updateJeopardyDisplay();
}

// Family Feud
let currentFeudCategoryIndex = 0;
let revealedFeudAnswers = [];

function initializeFamilyFeud() {
    currentFeudCategoryIndex = 0;
    feudTeam = 0;
    updateFamilyFeudDisplay();
    displayFeudCategory();
}

function updateFamilyFeudDisplay() {
    document.getElementById('current-feud-team').textContent = teams[feudTeam].charAt(0).toUpperCase() + teams[feudTeam].slice(1);
}

function displayFeudCategory() {
    const category = feudCategories[currentFeudCategoryIndex];
    document.getElementById('current-category').textContent = category.name;
    const boardContainer = document.getElementById('feud-board');
    boardContainer.innerHTML = '';
    revealedFeudAnswers = [];

    category.answers.forEach((answer, index) => {
        const answerDiv = document.createElement('div');
        answerDiv.classList.add('feud-answer');
        answerDiv.dataset.index = index;
        answerDiv.innerHTML = `<span>${index + 1}. _________</span><span class="feud-points">${answer.points}</span>`;
        answerDiv.onclick = () => revealFeudAnswer(index);
        boardContainer.appendChild(answerDiv);
    });
}

function revealFeudAnswer(index) {
    const category = feudCategories[currentFeudCategoryIndex];
    const answer = category.answers[index];
    const answerDiv = document.querySelectorAll('.feud-answer')[index];

    if (!revealedFeudAnswers.includes(index)) {
        answerDiv.innerHTML = `<span>${index + 1}. ${answer.text}</span><span class="feud-points">${answer.points}</span>`;
        answerDiv.classList.add('revealed');
        revealedFeudAnswers.push(index);
        adjustScore(teams[feudTeam], answer.points);
    }
}

function nextFeudCategory() {
    currentFeudCategoryIndex = (currentFeudCategoryIndex + 1) % feudCategories.length;
    displayFeudCategory();
}

function nextFeudTeam() {
    feudTeam = (feudTeam + 1) % teams.length;
    updateFamilyFeudDisplay();
}

function resetFeud() {
    initializeFamilyFeud();
}


